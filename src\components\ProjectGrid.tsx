
import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ProjectCard from './ProjectCard';

gsap.registerPlugin(ScrollTrigger);

// Define the project data structure
const projects = [
  {
    id: 1,
    title: "E-Commerce Platform for Fashion Hub",
    category: "Web Development",
    technologies: "React, Node.js, MongoDB",
    imageUrl: "/images/project-1.png",
    description: "An innovative platform built for a major fashion retailer, featuring an intuitive UX/UI design, secure payment gateway, and comprehensive product management. The solution streamlined their inventory system while providing a seamless shopping experience."
  },
  {
    id: 2,
    title: "FitLife App for Food Delivery Service",
    category: "Mobile App Development",
    technologies: "React Native, Firebase",
    imageUrl: "/images/project-2.png",
    description: "A healthy meal prep service app that allows customers to order customized meal plans based on their dietary requirements and fitness goals. Features include meal customization, subscription management, and real-time delivery tracking."
  },
  {
    id: 3,
    title: "Booking and Reservation System for Event Management",
    category: "Web Development",
    technologies: "Vue.js, Laravel",
    imageUrl: "/images/project-3.png",
    description: "An advanced booking system for a high-profile event management company. The solution lets users select venues, catering, decoration, and event management services. It helped the organization scale up their customer operations."
  },
  {
    id: 4,
    title: "Content Generator for Workflow Automation",
    category: "Design, Web Development",
    technologies: "React, Node.js, OpenAI",
    imageUrl: "/images/project-4.png",
    description: "A powerful content generator that helps marketers create high-quality content based on their brand guidelines and target audience. Features include template customization, AI-driven suggestions, and direct CMS integration."
  },
  {
    id: 5,
    title: "Web Portal for Real-Estate Listings",
    category: "Web Development",
    technologies: "Next.js, Supabase",
    imageUrl: "/images/project-5.png",
    description: "A dynamic real estate platform with property listing, advanced search functionality, virtual tours, and integrated payment options for deposits. The solution transformed the client's traditional business model to a digital-first approach."
  },
  {
    id: 6,
    title: "FitLife App for Fitness Tracking",
    category: "Mobile App Development",
    technologies: "Flutter, Supabase",
    imageUrl: "/images/project-6.png",
    description: "A comprehensive fitness tracking application that helps users monitor workouts, set goals, track nutrition, and connect with fitness coaches. The app includes workout plans, progress analytics, and social features."
  },
  {
    id: 7,
    title: "Online Software for Multi-Chain Management",
    category: "Web Development",
    technologies: "Angular, .NET Core",
    imageUrl: "/images/project-7.png",
    description: "A robust multi-location management solution for a retail chain with 50+ locations across the country. The platform offers inventory management, staff scheduling, performance analytics, and centralized operations control."
  },
  {
    id: 8,
    title: "Educational Platform for Online Learning",
    category: "Web Development",
    technologies: "React, Express, MongoDB",
    imageUrl: "/images/project-8.png",
    description: "A feature-rich e-learning platform with course creation tools, student management, interactive quizzes, and video conferencing capabilities. The solution helped the client transition from in-person to online education during the pandemic."
  },
  {
    id: 9,
    title: "Mobile App for Travel Planning",
    category: "Mobile App Development",
    technologies: "React Native, Node.js",
    imageUrl: "/images/project-9.png",
    description: "An intuitive travel app with itinerary planning, local recommendations, booking integration, and social sharing features. The application helped the travel agency increase their digital bookings by 45%."
  },
  {
    id: 10,
    title: "Web Application for Customer Relationship Management",
    category: "Web Development",
    technologies: "React, Express, PostgreSQL",
    imageUrl: "/images/project-10.png",
    description: "A customized CRM system designed for a financial services company with complex client management needs. Features include document management, automated communication flows, meeting scheduling, and analytics dashboards."
  },
];

const ProjectGrid = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const projectElements = sectionRef.current?.querySelectorAll('.project-card');
    
    if (projectElements) {
      gsap.fromTo(
        projectElements,
        { opacity: 0, y: 30 },
        { 
          opacity: 1, 
          y: 0, 
          duration: 0.6,
          stagger: 0.1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 75%",
          }
        }
      );
    }
    
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-left mb-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
            At KavaraDigital
          </h2>
          <p className="text-agency-white-muted max-w-3xl mb-8">
            We turn our passion for design into a creative approach to solve all
            technological challenges by producing beautiful, functional,
            user-centric solutions.
          </p>
          <p className="text-agency-white-muted">
            Here are some case studies of what we've built so far:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-16">
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      </div>
    </section>
  )
};

export default ProjectGrid;
