import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import PricingHero from '@/components/PricingHero'
import PricingSection from '@/components/PricingSection'
import FaqSection from '@/components/FaqSection'
import ContactCta from '@/components/ContactCta'
import { Link } from 'react-router-dom'

gsap.registerPlugin(ScrollTrigger)

const Pricing = () => {
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />
      <PricingHero />
      <PricingSection />
      <div className="bg-agency-darker py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-8 text-white">
            Ready to Get Started?
          </h2>
          <p className="text-agency-white-muted max-w-3xl mx-auto mb-10">
            See how we've helped our clients transform their digital presence
            with innovative solutions. Visit our portfolio to see the results
            we’ve delivered and find the perfect plan for your needs.
          </p>
          <Link
            to="/work"
            className="inline-flex px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            See Our Work
          </Link>
        </div>
      </div>
      <FaqSection />
      <ContactCta />
      <Footer />
    </div>
  )
}

export default Pricing
