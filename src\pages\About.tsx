
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import AboutHero from '@/components/AboutHero';
import AboutContent from '@/components/AboutContent';
import AboutStory from '@/components/AboutStory';

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden text-white">
      <Navbar />
      <AboutHero />
      <AboutContent />
      <AboutStory />
      <div className="bg-agency-darker py-12 text-center">
        <div className="container mx-auto px-4 max-w-3xl">
          <div className="flex items-center justify-center mb-5">
            <div className="w-12 h-12 rounded bg-agency-green flex items-center justify-center mr-3">
              <span className="text-agency-dark font-bold text-2xl">K</span>
            </div>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
            Today, KavaraDigital Continues to Thrive as a Leading Digital
            Product Agency....
          </h2>
          <p className="text-agency-white-muted mb-8">
            Combining the power of design, engineering, and project management
            to create transformative digital experiences. They invite you to
            join them on your journey and discover how they can help bring your
            digital ideas to life.
          </p>
        </div>
      </div>
      <div className="bg-agency-dark py-10 text-center border-t border-agency-white-muted/10">
        <div className="container mx-auto px-4">
          <p className="text-agency-white-muted mb-6">
            Welcome to KavaraDigital - Where collaboration, Expertise, and
            Client-Centricity Intersect to Shape the Future of Digital
            Innovation.
          </p>
          <a
            href="/#contact"
            className="inline-flex px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Start Project
          </a>
        </div>
      </div>
      <Footer />
    </div>
  )
};

export default About;
