
import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ServiceDetailSection from '@/components/ServiceDetailSection';
import ContactCta from '@/components/ContactCta';
import AnimatedFloatingBlocks from '@/components/AnimatedFloatingBlocks'

gsap.registerPlugin(ScrollTrigger)

const Services = () => {
  useEffect(() => {
    // Initialize any global GSAP animations or ScrollTriggers for this page

    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden pb-20 md:pb-0">
      <Navbar />
      <div className="pt-24 pb-16 bg-gradient-overlay relative overflow-hidden">
        {/* Animated Floating Blocks */}
        <AnimatedFloatingBlocks />

        <div className="container mx-auto px-4 py-44 text-center relative z-10">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white ">
            Our Services
          </h1>
          <p className="text-agency-white-muted max-w-3xl mx-auto">
            We help startups and established brands transform their digital
            presence through design, engineering, and strategic project
            management.
          </p>
        </div>
      </div>

      <ServiceDetailSection
        title="Design"
        id="design"
        description="We create intuitive, user-centered designs that engage users, meet business goals, and enhance digital experiences. Our design approach combines creativity with strategic thinking to deliver memorable visual solutions."
        services={[
          {
            category: 'User Experience (UX) Design',
            items: [
              { title: 'User Research and Persona Development', icon: 'User' },
              {
                title: 'Information Architecture and Wireframe',
                icon: 'LayoutGrid'
              },
              {
                title: 'Interaction Prototyping and User Testing',
                icon: 'Pen'
              },
              { title: 'UI Design and Visual Hierarchy', icon: 'Layers' }
            ]
          },
          {
            category: 'User Interface (UI) Design',
            items: [
              { title: 'Responsive Web Design', icon: 'LayoutList' },
              { title: 'Mobile Interface and Tablet UI', icon: 'Smartphone' },
              { title: 'Microinteractions and Focus States', icon: 'Pointer' },
              { title: 'Design Systems Development', icon: 'Settings' }
            ]
          },
          {
            category: 'Branding and Identity',
            items: [
              {
                title: 'Logo Design and Brand Identity Development',
                icon: 'Pencil'
              },
              { title: 'Brand Strategy and Positioning', icon: 'Target' },
              { title: 'Brand Guidelines Style Guide', icon: 'FileText' },
              {
                title: 'Marketing Collateral Design (Web and Print)',
                icon: 'FileCode'
              }
            ]
          }
        ]}
      />

      <ServiceDetailSection
        title="Engineering"
        id="engineering"
        description="Our development team transforms ideas into elegant applications built with modern technologies to deliver scalable and robust solutions. We ensure your products are both powerful and maintainable."
        services={[
          {
            category: 'Web Development',
            items: [
              {
                title: 'Frontend Development (React, Vue, Angular)',
                icon: 'Code'
              },
              {
                title: 'Backend Development (Python, Node.js)',
                icon: 'Database'
              },
              {
                title: 'Fullstack Development (MERN/LAMP Stack, Django)',
                icon: 'Layers3'
              },
              {
                title: 'E-commerce Solutions (Shopify/WooCommerce, Magento)',
                icon: 'ShoppingCart'
              }
            ]
          },
          {
            category: 'Mobile App Development',
            items: [
              {
                title: 'Native iOS and Android App Development',
                icon: 'Smartphone'
              },
              {
                title: 'Cross-platform App Development (React Native, Flutter)',
                icon: 'Layers'
              },
              { title: 'Progressive Web Apps (PWAs)', icon: 'Globe' },
              {
                title: 'App Testing, Deployment, and Distribution',
                icon: 'CheckCircle'
              }
            ]
          },
          {
            category: 'Custom Software Development',
            items: [
              { title: 'Enterprise Software Development', icon: 'Building' },
              {
                title: 'SaaS (Software-as-a-Service) Applications',
                icon: 'Cloud'
              },
              {
                title: 'Integration with Third-Party APIs and Services',
                icon: 'Link'
              },
              {
                title: 'Legacy Systems Modernization and Migration',
                icon: 'RefreshCw'
              }
            ]
          }
        ]}
      />

      <ServiceDetailSection
        title="Project Management"
        id="project-management"
        description="Successful digital products require careful planning, execution, and monitoring. Our project management team ensures your project stays on track, on budget, and aligned with your business objectives."
        services={[
          {
            category: 'Project Planning and Scoping',
            items: [
              {
                title: 'Requirements Analysis and Definition',
                icon: 'ClipboardList'
              },
              {
                title: 'Project Roadmap and Timeline Development',
                icon: 'CalendarDays'
              },
              {
                title: 'Resource Allocation and Team Assignment',
                icon: 'Users'
              },
              {
                title: 'Risk Assessment and Mitigation Strategies',
                icon: 'Shield'
              }
            ]
          },
          {
            category: 'Agile Development',
            items: [
              { title: 'Sprint Execution and Sprint Planning', icon: 'Timer' },
              {
                title: 'Daily or Weekly Technology Implementation',
                icon: 'CalendarCheck'
              },
              {
                title: 'Product Progress Updates and Review',
                icon: 'ChartBar'
              },
              {
                title: 'Continuous Improvement and Iteration',
                icon: 'RefreshCw'
              }
            ]
          },
          {
            category: 'Quality Assurance and Testing',
            items: [
              { title: 'Test Strategy Design', icon: 'TestTube' },
              { title: 'Functionality Testing', icon: 'CheckSquare' },
              {
                title: 'Performance and Load Testing',
                icon: 'BarChartHorizontal'
              },
              {
                title: 'Usability and User Interface Testing',
                icon: 'UserCheck'
              }
            ]
          }
        ]}
      />

      <ContactCta />
      <Footer />
    </div>
  )
}

export default Services;
