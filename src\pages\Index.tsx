
import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import HeroSection from '@/components/HeroSection';
import ClientsSection from '@/components/ClientsSection';
import ServicesSection from '@/components/ServicesSection';
import FeaturesSection from '@/components/FeaturesSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import FaqSection from '@/components/FaqSection';
import ContactCta from '@/components/ContactCta';
import Footer from '@/components/Footer'

gsap.registerPlugin(ScrollTrigger)

const Index = () => {
  useEffect(() => {
    // Initialize any global GSAP animations or ScrollTriggers here

    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />
      <HeroSection />
      <ClientsSection />
      <ServicesSection />
      <FeaturesSection />
      <TestimonialsSection />
      <FaqSection />
      <ContactCta />
      <Footer />
    </div>
  )
}

export default Index;
