
import React from 'react';
import { Mail, Phone, MapPin, Twitter, Instagram, Linkedin } from 'lucide-react';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-agency-darker pt-16 pb-8 text-agency-white-muted">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
          <div>
            <Link to="/" className="flex items-center mb-6">
              <div className="w-10 h-10 rounded bg-agency-green flex items-center justify-center mr-2">
                <span className="text-agency-dark font-bold text-xl">K</span>
              </div>
              <span className="text-white font-bold text-xl">
                KavaraDigital
              </span>
            </Link>
            <p className="mb-6">
              We design and develop exceptional digital experiences that make an
              impact.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                aria-label="Twitter"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <Twitter size={20} />
              </a>
              <a
                href="#"
                aria-label="Instagram"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <Instagram size={20} />
              </a>
              <a
                href="#"
                aria-label="LinkedIn"
                className="text-agency-white-muted hover:text-agency-green"
              >
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">Pages</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="hover:text-agency-green">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="hover:text-agency-green">
                  About
                </Link>
              </li>
              <li>
                <Link to="/services" className="hover:text-agency-green">
                  Services
                </Link>
              </li>
              <li>
                <Link to="/work" className="hover:text-agency-green">
                  Work
                </Link>
              </li>
              <li>
                <Link to="/process" className="hover:text-agency-green">
                  Process
                </Link>
              </li>
              <li>
                <Link to="/contact" className="hover:text-agency-green">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">Services</h3>
            <ul className="space-y-3">
              {[
                'UI/UX Design',
                'Web Development',
                'Mobile Apps',
                'Branding',
                'Product Strategy',
                'Maintenance'
              ].map((item) => (
                <li key={item}>
                  <Link to="/services" className="hover:text-agency-green">
                    {item}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-bold mb-6">Contact</h3>
            <ul className="space-y-4">
              <li className="flex items-center">
                <Mail size={18} className="mr-3 text-agency-green" />
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-white"
                >
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-3 text-agency-green" />
                <a href="tel:+919123232308" className="hover:text-white">
                  +91 (9123) 23-2308
                </a>
              </li>
              <li className="flex items-start">
                <MapPin size={18} className="mr-3 mt-1 text-agency-green" />
                <address className="not-italic">
                  123 Design Street
                  <br />
                  San Francisco, CA 94103
                </address>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} KavaraDigital. All rights
            reserved.
          </p>
          <div className="flex space-x-6">
            <a href="#" className="hover:text-white">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-white">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
};

export default Footer;
