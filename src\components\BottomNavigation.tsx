import React, { useEffect, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { gsap } from 'gsap'
import {
  Home,
  Briefcase,
  DollarSign,
  GitBranch,
  FolderOpen,
  Users,
  MessageCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

const BottomNavigation = () => {
  const location = useLocation()
  const navRef = useRef<HTMLDivElement>(null)
  const indicatorRef = useRef<HTMLDivElement>(null)

  const navItems = [
    { name: 'Home', path: '/', icon: Home },
    { name: 'Services', path: '/services', icon: Briefcase },
    { name: 'Pricing', path: '/pricing', icon: DollarSign },
    { name: 'Process', path: '/process', icon: GitBranch },
    { name: 'Work', path: '/work', icon: FolderOpen },
    { name: 'About', path: '/about', icon: Users },
    { name: 'Contact', path: '/contact', icon: MessageCircle }
  ]

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  useEffect(() => {
    // Initial animation when component mounts
    if (navRef.current) {
      gsap.fromTo(
        navRef.current,
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out' }
      )
    }
  }, [])

  useEffect(() => {
    // Animate active indicator
    const activeIndex = navItems.findIndex((item) => isActive(item.path))
    if (activeIndex !== -1 && indicatorRef.current) {
      const itemWidth = 100 / navItems.length
      const translateX = activeIndex * itemWidth

      gsap.to(indicatorRef.current, {
        x: `${translateX}%`,
        duration: 0.3,
        ease: 'power2.out'
      })
    }
  }, [location.pathname])

  const handleItemClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    const target = event.currentTarget

    // Add click animation
    gsap.to(target, {
      scale: 0.9,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut'
    })
  }

  return (
    <nav
      ref={navRef}
      className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-agency-darker/95 backdrop-blur-md border-t border-agency-green/20 shadow-lg shadow-agency-dark/50"
    >
      {/* Active indicator */}
      <div
        ref={indicatorRef}
        className="absolute top-0 left-0 h-0.5 bg-agency-green transition-all duration-300"
        style={{ width: `${100 / navItems.length}%` }}
      />

      <div className="flex items-center justify-around py-3 px-2">
        {navItems.map((item) => {
          const Icon = item.icon
          const isItemActive = isActive(item.path)

          return (
            <Link
              key={item.name}
              to={item.path}
              onClick={handleItemClick}
              className={cn(
                'flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 min-w-0 flex-1 active:scale-95',
                isItemActive
                  ? 'text-agency-green bg-agency-green/10'
                  : 'text-agency-white-muted hover:text-agency-green hover:bg-agency-green/5'
              )}
            >
              <Icon
                size={20}
                className={cn(
                  'mb-1 transition-all duration-200',
                  isItemActive ? 'scale-110' : 'scale-100'
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium transition-all duration-200 truncate',
                  isItemActive ? 'text-agency-green' : 'text-agency-white-muted'
                )}
              >
                {item.name}
              </span>
            </Link>
          )
        })}
      </div>
    </nav>
  )
}

export default BottomNavigation
